import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smartfarming_bapeltan/screens/home.dart';
import 'package:smartfarming_bapeltan/screens/main_navigation.dart';
import 'package:smartfarming_bapeltan/screens/splash_screen.dart';
import 'package:smartfarming_bapeltan/services/data_manager.dart';

/// main program untuk memulai aplikasi
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize DataManager singleton
  await DataManager().initialize();

  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp])
      .then((_) {
    runApp(const BapeltanApp());
  });
  // runApp(MaterialApp(
  //   home: Home(),
  // ));
}

class BapeltanApp extends StatelessWidget {
  const BapeltanApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: "<PERSON>peltanApp",
      theme: ThemeData(
        fontFamily: 'Montserrat',
      ),
      home: SplashScreen(),
    );
  }
}
