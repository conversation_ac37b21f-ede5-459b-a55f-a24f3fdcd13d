import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartfarming_bapeltan/common/url.dart';

/// Singleton class untuk mengelola semua data polling dan network requests
/// Menghindari multiple polling timers dan network requests berlebihan
class DataManager {
  static final DataManager _instance = DataManager._internal();
  factory DataManager() => _instance;
  DataManager._internal();

  // Stream controllers untuk data sharing
  final StreamController<Map<String, dynamic>> _realtimeDataController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _controlDataController = 
      StreamController<Map<String, dynamic>>.broadcast();

  // Polling timer - hanya satu untuk seluruh aplikasi
  Timer? _pollingTimer;
  bool _isPollingActive = false;
  bool _isAppInForeground = true;

  // Cache untuk mengurangi SharedPreferences access
  String? _cachedAuthToken;
  String? _cachedDeviceId;
  String? _cachedUsername;
  String? _cachedNamaAlat;
  int? _cachedIdUser;
  int? _cachedIdAlat;

  // Request management
  bool _isRealtimeLoading = false;
  bool _isControlLoading = false;
  DateTime? _lastRealtimeRequest;
  DateTime? _lastControlRequest;
  
  // Minimum interval between requests (prevent spam)
  static const Duration _minRequestInterval = Duration(seconds: 3);

  // Getters untuk streams
  Stream<Map<String, dynamic>> get realtimeDataStream => _realtimeDataController.stream;
  Stream<Map<String, dynamic>> get controlDataStream => _controlDataController.stream;

  // Getters untuk cached data
  String? get cachedAuthToken => _cachedAuthToken;
  String? get cachedDeviceId => _cachedDeviceId;
  String? get cachedUsername => _cachedUsername;
  String? get cachedNamaAlat => _cachedNamaAlat;
  int? get cachedIdUser => _cachedIdUser;
  int? get cachedIdAlat => _cachedIdAlat;

  /// Initialize data manager dan load cached preferences
  Future<void> initialize() async {
    await _loadCachedPreferences();
  }

  /// Load preferences ke cache untuk mengurangi disk access
  Future<void> _loadCachedPreferences() async {
    try {
      SharedPreferences pref = await SharedPreferences.getInstance();
      _cachedAuthToken = pref.getString('authToken');
      _cachedDeviceId = pref.getString('deviceId');
      _cachedUsername = pref.getString('username');
      _cachedNamaAlat = pref.getString('namaAlat');
      _cachedIdUser = pref.getInt('id');
      _cachedIdAlat = pref.getInt('idAlat');
    } catch (e) {
      print('Error loading cached preferences: $e');
    }
  }

  /// Update cached preferences
  Future<void> updateCachedPreferences({
    String? authToken,
    String? deviceId,
    String? username,
    String? namaAlat,
    int? idUser,
    int? idAlat,
  }) async {
    if (authToken != null) _cachedAuthToken = authToken;
    if (deviceId != null) _cachedDeviceId = deviceId;
    if (username != null) _cachedUsername = username;
    if (namaAlat != null) _cachedNamaAlat = namaAlat;
    if (idUser != null) _cachedIdUser = idUser;
    if (idAlat != null) _cachedIdAlat = idAlat;
  }

  /// Start polling - hanya satu timer untuk seluruh aplikasi
  void startPolling() {
    if (_isPollingActive) return;
    
    _stopPolling();
    _isPollingActive = true;
    
    if (_isAppInForeground) {
      // Polling setiap 5 detik
      _pollingTimer = Timer.periodic(Duration(seconds: 5), (_) {
        if (_isAppInForeground && _isPollingActive) {
          _fetchAllData();
        }
      });
      
      // Load data immediately
      _fetchAllData();
    }
  }

  /// Stop polling
  void stopPolling() {
    _stopPolling();
    _isPollingActive = false;
  }

  void _stopPolling() {
    _pollingTimer?.cancel();
    _pollingTimer = null;
  }

  /// Set app foreground state
  void setAppForegroundState(bool isInForeground) {
    _isAppInForeground = isInForeground;
    
    if (isInForeground && _isPollingActive) {
      startPolling();
    } else if (!isInForeground) {
      _stopPolling();
    }
  }

  /// Fetch all data (realtime + control) dengan debouncing
  Future<void> _fetchAllData() async {
    // Fetch realtime data
    _fetchRealtimeData();
    
    // Fetch control data dengan slight delay untuk menghindari concurrent requests
    Future.delayed(Duration(milliseconds: 500), () {
      _fetchControlData();
    });
  }

  /// Fetch realtime data dengan debouncing dan caching
  Future<void> _fetchRealtimeData() async {
    // Debouncing - cek apakah request terlalu sering
    if (_isRealtimeLoading || 
        (_lastRealtimeRequest != null && 
         DateTime.now().difference(_lastRealtimeRequest!) < _minRequestInterval)) {
      return;
    }

    _isRealtimeLoading = true;
    _lastRealtimeRequest = DateTime.now();

    try {
      var result = await _performRealtimeRequest();
      _realtimeDataController.add(result);
    } catch (e) {
      print('Error fetching realtime data: $e');
      _realtimeDataController.add({
        'realtimeData': [],
        'totalItems': 0,
        'error': e.toString(),
        'date': _formatDate(DateTime.now()),
        'time': _formatTime(DateTime.now())
      });
    } finally {
      _isRealtimeLoading = false;
    }
  }

  /// Fetch control data dengan debouncing dan caching
  Future<void> _fetchControlData() async {
    // Debouncing - cek apakah request terlalu sering
    if (_isControlLoading || 
        (_lastControlRequest != null && 
         DateTime.now().difference(_lastControlRequest!) < _minRequestInterval)) {
      return;
    }

    _isControlLoading = true;
    _lastControlRequest = DateTime.now();

    try {
      var result = await _performControlRequest();
      _controlDataController.add(result);
    } catch (e) {
      print('Error fetching control data: $e');
      _controlDataController.add({
        'kontrol': [],
        'totalItems': 0,
        'error': e.toString()
      });
    } finally {
      _isControlLoading = false;
    }
  }

  /// Perform actual realtime data request
  Future<Map<String, dynamic>> _performRealtimeRequest({int retryCount = 0}) async {
    const int maxRetries = 2; // Reduced from 3
    const Duration retryDelay = Duration(seconds: 1); // Reduced from 2

    try {
      // Use cached deviceId to avoid SharedPreferences access
      if (_cachedDeviceId == null) {
        await _loadCachedPreferences();
      }

      String apiURL = UrlData().url_sensor_data +
          '/records?filter=(deviceId="${_cachedDeviceId}")&sort=-timestamp&perPage=10';

      Map<String, String> headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
      };

      if (_cachedAuthToken != null) {
        headers['Authorization'] = 'Bearer $_cachedAuthToken';
      }

      final response = await http.get(Uri.parse(apiURL), headers: headers).timeout(
        Duration(seconds: 8), // Reduced timeout
        onTimeout: () {
          throw TimeoutException('Request timeout', Duration(seconds: 8));
        },
      );

      if (response.statusCode == 200) {
        var jsonData = json.decode(response.body);

        // Convert PocketBase format to legacy format
        var convertedItems = [];
        for (var item in jsonData['items'] ?? []) {
          convertedItems.add({
            'idSensor': item['id'],
            'namaSensor': item['sensorType'] ?? 'Unknown Sensor',
            'nilaiSensor': item['value'] ?? 0,
            'satuanSensor': item['unit'] ?? '',
            'waktuSensor': item['timestamp'] ?? item['created'],
            'idAlat': _cachedIdAlat ?? 0,
            'namaAlat': _cachedNamaAlat ?? 'Unknown Device'
          });
        }

        return {
          'realtimeData': convertedItems,
          'totalItems': jsonData['totalItems'] ?? 0,
          'date': _formatDate(DateTime.now()),
          'time': _formatTime(DateTime.now())
        };
      } else if (response.statusCode == 401) {
        return {'realtimeData': [], 'totalItems': 0, 'error': 'auth_error'};
      } else {
        throw HttpException('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      if (retryCount < maxRetries &&
          (e is SocketException || e is TimeoutException || e is HttpException)) {
        await Future.delayed(retryDelay);
        return _performRealtimeRequest(retryCount: retryCount + 1);
      }

      return {
        'realtimeData': [],
        'totalItems': 0,
        'error': e.toString(),
        'date': _formatDate(DateTime.now()),
        'time': _formatTime(DateTime.now())
      };
    }
  }

  /// Perform actual control data request
  Future<Map<String, dynamic>> _performControlRequest({int retryCount = 0}) async {
    const int maxRetries = 2; // Reduced from 3
    const Duration retryDelay = Duration(seconds: 1); // Reduced from 2

    try {
      // Use cached deviceId to avoid SharedPreferences access
      if (_cachedDeviceId == null) {
        await _loadCachedPreferences();
      }

      String apiURL = UrlData().url_controls + '/records?filter=(deviceId="${_cachedDeviceId}")';

      Map<String, String> headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
      };

      if (_cachedAuthToken != null) {
        headers['Authorization'] = 'Bearer $_cachedAuthToken';
      }

      final response = await http.get(Uri.parse(apiURL), headers: headers).timeout(
        Duration(seconds: 8), // Reduced timeout
        onTimeout: () {
          throw TimeoutException('Request timeout', Duration(seconds: 8));
        },
      );

      if (response.statusCode == 200) {
        var jsonData = json.decode(response.body);

        // Convert PocketBase format to legacy format
        var convertedItems = [];
        for (var item in jsonData['items'] ?? []) {
          convertedItems.add({
            'idKontrol': item['id'],
            'namaKontrol': item['namaKontrol'] ?? 'Unknown Control',
            'isON': item['isON'] ?? false,
            'automated': item['automated'] ?? false,
            'parameter': item['parameter'] ?? '',
            'idAlat': _cachedIdAlat ?? 0,
            'namaAlat': _cachedNamaAlat ?? 'Unknown Device'
          });
        }

        return {
          'kontrol': convertedItems,
          'totalItems': jsonData['totalItems'] ?? 0
        };
      } else if (response.statusCode == 401) {
        return {'kontrol': [], 'totalItems': 0, 'error': 'auth_error'};
      } else {
        throw HttpException('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      if (retryCount < maxRetries &&
          (e is SocketException || e is TimeoutException || e is HttpException)) {
        await Future.delayed(retryDelay);
        return _performControlRequest(retryCount: retryCount + 1);
      }

      return {
        'kontrol': [],
        'totalItems': 0,
        'error': e.toString()
      };
    }
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Dispose resources
  void dispose() {
    _stopPolling();
    _realtimeDataController.close();
    _controlDataController.close();
  }
}
